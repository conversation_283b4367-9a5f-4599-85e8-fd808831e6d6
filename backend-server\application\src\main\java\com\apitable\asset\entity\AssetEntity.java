/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * Resource Table.
 * </p>
 *
 * <AUTHOR> Generator Tool
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode
@TableName(keepGlobalPrefix = true, value = "asset")
public class AssetEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key.
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * Hash and MD5 summary of the whole file.
     */
    private String checksum;

    /**
     * Base64 of the first 32 bytes of the resource file.
     */
    private String headSum;

    /**
     * Bucket Tag.
     */
    private String bucket;

    /**
     * Bucket name.
     */
    private String bucketName;

    /**
     * File size (unit: byte).
     */
    private Integer fileSize;

    /**
     * Cloud file storage path.
     */
    private String fileUrl;

    /**
     * MimeType.
     */
    private String mimeType;

    /**
     * File extension.
     */
    private String extensionName;

    /**
     * Preview Token.
     */
    private String preview;

    /**
     * Is it a template attachment(0:No,1:Yes).
     */
    private Boolean isTemplate;

    /**
     * Image Height.
     */
    private Integer height;

    /**
     * Image Width.
     */
    private Integer width;

    /**
     * Delete tag(0:No,1:Yes).
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * Create Time.
     */
    private LocalDateTime createdAt;

}
