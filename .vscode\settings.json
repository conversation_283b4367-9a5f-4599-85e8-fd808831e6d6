{
  "typescript.tsdk": ".yarn/sdks/typescript/lib",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": false,
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.tabSize": 2,
  "debug.node.autoAttach": "on",
  "cSpell.words": [
    "ahooks",
    "ARRAYCOMPACT",
    "arrayjoin",
    "ARRAYUNIQUE",
    "attname",
    "changesets",
    "Colla",
    "contexify",
    "coord",
    "counta",
    "countall",
    "DATAPACK",
    "datasheet",
    "datasheets",
    "dataspace",
    "DATEADD",
    "DATETIME",
    "droppable",
    "errcode",
    "errmsg",
    "Excape",
    "exceljs",
    "fastify",
    "Figma",
    "Flexbox",
    "fuzzer",
    "Gantt",
    "grpc",
    "IAPI",
    "id",
    "IJOT",
    "image",
    "immer",
    "jsonpointer",
    "Kanban",
    "KONVA",
    "login",
    "mget",
    "mset",
    "msgtype",
    "nacos",
    "nestjs",
    "overscan",
    "pexpire",
    "photoshop",
    "postbuild",
    "powerpoint",
    "prismjs",
    "Renamable",
    "resizer",
    "rgba",
    "rjsf",
    "sadd",
    "sanpshot",
    "scard",
    "scriptjs",
    "sismember",
    "smembers",
    "sprintf",
    "srem",
    "Submenu",
    "swiper",
    "Thumbor",
    "tinyint",
    "tkey",
    "typeorm",
    "umijs",
    "undoable",
    "Unmount",
    "untilde",
    "urlcat",
    "USERINFO",
    "uuids",
    "WECHAT",
    "Wecom",
    "weixin",
    "XSRF",
    "xy"
  ],
  "search.exclude": {
    "**/.yarn": true,
    "**/.pnp.*": true
  },
  "eslint.nodePath": ".yarn/sdks",
  "typescript.enablePromptUseWorkspaceTsdk": true,
}
