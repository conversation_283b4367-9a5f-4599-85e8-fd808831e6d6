# APITable 表格分组功能实现方案报告

## 概述

APITable 是一个开源的多维表格项目，其表格分组功能是一个核心特性，允许用户按照字段值对数据进行分组显示。本报告详细分析了该功能的实现架构、核心代码和技术方案。

## 1. 核心数据结构

### 1.1 分组信息类型定义

```typescript
// packages/core/src/types/view_types.ts
export interface ISortedField {
  fieldId: string;  // 分组字段ID
  desc: boolean;    // 是否降序排列
}

export type IGroupInfo = ISortedField[];  // 分组信息数组，支持多级分组
```

### 1.2 分组核心类

```typescript
// packages/core/src/model/view/group.ts
export class Group {
  groupBreakpoint: { [key: string]: number[] };  // 分组断点数据
  groupArray: string[] = [];                     // 分组字段ID数组
  groupTabIdMap: Map<string, boolean> = new Map(); // 分组标签映射
  
  constructor(groupInfo: IGroupInfo, groupBreakpoint?: { [key: string]: number[] })
}
```

### 1.3 线性行数据结构

```typescript
// 分组后的线性行结构，用于渲染
export interface ILinearRow {
  type: CellType;     // 行类型：Record、GroupTab、Blank、Add
  depth: number;      // 分组层级深度
  recordId: string;   // 记录ID
}
```

## 2. 分组算法实现

### 2.1 分组计算核心逻辑

分组计算主要在 `packages/core/src/compute_manager/view_derivate/slice/view_group_derivate.ts` 中实现：

```typescript
private getLinearRowsAndGroup(view: IViewProperty, visibleRows: IViewRow[], recordMoveType: RecordMoveType) {
  const groupInfo = view.groupInfo || [];
  const linearRows: ILinearRow[] = [];
  const groupSketch = new Group(groupInfo);
  
  // 遍历所有可见行，检测分组断点
  for (const [index, row] of [...visibleRows, lastRow].entries()) {
    let shouldGenGroupLinearRows = false;
    
    // 检查每个分组字段是否发生变化
    groupInfo.forEach((groupItem, groupItemIndex) => {
      const fieldId = groupItem.fieldId;
      const field = getField(state, fieldId, this.datasheetId);
      const cv1 = this.getFixedCellValue(recordMoveType, preRow.recordId, fieldId);
      const cv2 = this.getFixedCellValue(recordMoveType, row.recordId, fieldId);
      
      // 使用字段的compare方法比较值是否相同
      if (!Field.bindContext(field, state).compare(cv1, cv2) === 0) {
        shouldGenGroupLinearRows = true;
        // 添加分组断点
        groupSketch.addBreakpointAndSetGroupTab(groupItem.fieldId, index, row.recordId, groupItemIndex);
      }
    });
    
    // 生成分组结构的线性行
    if (shouldGenGroupLinearRows) {
      const groupLinearRows = groupSketch.genGroupLinearRows(index, row.recordId, preRow.recordId);
      linearRows.push(...groupLinearRows);
    }
  }
}
```

### 2.2 分组排序算法

```typescript
// packages/core/src/compute_manager/view_derivate/view_derivate_base.ts
private getSortRowsByGroup(view: IViewProperty, rows: IViewRow[]) {
  const groups = getGroupFields(view, snapshot.meta.fieldMap, fieldPermissionMap);
  
  return rows.sort((row1, row2) => {
    return groups.reduce((prev, field, index) => {
      if (prev !== 0) return prev;
      
      const fieldInstance = Field.bindContext(field, state);
      const cv1 = getCellValue(state, snapshot, row1.recordId, field.id);
      const cv2 = getCellValue(state, snapshot, row2.recordId, field.id);
      
      const res = fieldInstance.compare(cv1, cv2);
      const sign = descOrders[index] ? -1 : 1;
      
      return res * sign;
    }, 0) || 1;
  });
}
```

## 3. UI组件实现

### 3.1 分组设置组件

分组设置界面位于 `packages/datasheet/src/pc/components/tool_bar/view_sort_and_group/view_group/view_group.tsx`：

```typescript
export const ViewGroup: React.FC<IViewSetting> = (props) => {
  const activeViewGroupInfo = useAppSelector((state) => Selectors.getActiveViewGroupInfo(state));
  
  const submitGroup = useCallback((data: IGroupInfo | null) => {
    executeCommandWithMirror(() => {
      resourceService.instance!.commandManager.execute({
        cmd: CollaCommandName.SetGroup,
        viewId: activityViewId,
        data: data || undefined,
      });
    });
  }, [activityViewId]);
  
  // 支持拖拽排序分组字段
  const onDragEnd = useCallback((result: DropResult) => {
    const { source, destination } = result;
    if (!destination) return;
    
    submitGroup(produce(activeViewGroupInfo, (draft) => {
      draft.splice(destination.index, 0, draft.splice(source.index, 1)[0]);
      return draft;
    }));
  }, [submitGroup, activeViewGroupInfo]);
}
```

### 3.2 分组标签组件

分组标签渲染组件位于多个文件中，支持不同视图类型：

- Grid视图：`packages/datasheet/src/pc/components/multi_grid/cell/virtual_cell/cell_group_tab/group_tab/group_tab.tsx`
- Konva Grid：`packages/datasheet/src/pc/components/konva_grid/components/cell/cell_other/group_tab.tsx`
- Gantt视图：`packages/datasheet/src/pc/components/gantt_view/components/task_group_header/task_group_header.tsx`

### 3.3 分组展开/折叠功能

```typescript
const changeGroupCollapseState = useCallback((newState: string[]) => {
  if (isSearching) return; // 搜索时禁用分组操作
  
  dispatch(StoreActions.setGroupingCollapse(datasheetId, newState));
  dispatch(StoreActions.setHoverRecordId(datasheetId, null));
  setStorage(StorageName.GroupCollapse, { [`${datasheetId},${viewId}`]: newState });
}, [datasheetId, viewId, isSearching]);
```

## 4. 状态管理

### 4.1 Redux Actions

分组相关的Redux Actions定义在 `packages/core/src/modules/database/store/actions/resource/datasheet/datasheet.ts`：

```typescript
export const setGroupingCollapse = (datasheetId: string, payload: string[]) => ({
  type: SET_GROUPING_COLLAPSE,
  datasheetId,
  payload,
});

export const setKanbanGroupingExpand = (datasheetId: string, payload: string[]) => ({
  type: SET_KANBAN_GROUPING_EXPAND,
  datasheetId,
  payload,
});
```

### 4.2 命令模式

分组设置使用命令模式实现，支持撤销/重做：

```typescript
// packages/core/src/commands/datasheet/set_group.ts
export const setGroup: ICollaCommandDef<ISetGroupOptions> = {
  undoable: true,
  
  execute: (context, options) => {
    const { state } = context;
    const { data, viewId } = options;
    const datasheetId = getActiveDatasheetId(state)!;
    
    // 验证权限和字段有效性
    // 执行分组设置
    return {
      result: ExecuteResult.Success,
      actions: [DatasheetActions.setViewProperty2Action(snapshot, { groupInfo: data }, viewId)]
    };
  }
};
```

## 5. 不同视图类型的分组支持

### 5.1 Grid视图
- 支持最多3级分组
- 分组标签显示在左侧，支持展开/折叠
- 支持分组内统计

### 5.2 Gallery视图
- 仅支持1级分组
- 按分组值将卡片分组显示

### 5.3 Kanban视图
- 基于单选字段进行分组
- 每个分组值对应一个看板列
- 支持拖拽调整分组顺序

### 5.4 Gantt视图
- 支持多级分组
- 分组标签显示为甘特图中的分组条

## 6. 性能优化

### 6.1 计算缓存
```typescript
// 使用计算缓存避免重复计算
clearGroupBreakpoint() {
  computeCache.delete('groupBreakpoint');
}

cacheGroupBreakpoint() {
  computeCache.set('groupBreakpoint', this.groupBreakpoint);
}
```

### 6.2 虚拟化渲染
- 大数据量时使用虚拟滚动
- 只渲染可见区域的分组和记录

### 6.3 增量更新
- 记录移动时只重新计算受影响的分组
- 使用断点机制快速定位分组边界

## 7. 技术特点

1. **多级分组支持**：最多支持3级分组，每级可选择不同字段
2. **字段类型兼容**：支持文本、数字、选项、成员等多种字段类型分组
3. **实时计算**：数据变化时自动重新计算分组结构
4. **状态持久化**：分组展开/折叠状态保存到本地存储
5. **权限控制**：根据字段权限控制分组操作
6. **搜索兼容**：搜索时禁用分组操作避免冲突

## 8. 总结

APITable的表格分组功能采用了清晰的分层架构，通过Group类管理分组逻辑，使用线性行结构统一不同视图的渲染，结合Redux进行状态管理，实现了功能完整、性能良好的分组系统。该实现方案具有良好的扩展性和维护性，是多维表格分组功能的优秀实践。
